import { useMemo } from 'react';
import { useI18n } from './useI18n';
import { 
  StackNavigationOptions, 
  TransitionPresets,
  CardStyleInterpolators,
  HeaderStyleInterpolators,
} from '@react-navigation/stack';
import { BottomTabNavigationOptions } from '@react-navigation/bottom-tabs';
import { DrawerNavigationOptions } from '@react-navigation/drawer';

export interface RTLNavigationConfig {
  respectRTL?: boolean;
  isRTL?: boolean;
  enableCustomTransitions?: boolean;
}

export interface RTLNavigationHelpers {
  isRTL: boolean;
  getStackOptions: (config?: RTLNavigationConfig) => Partial<StackNavigationOptions>;
  getTabOptions: (config?: RTLNavigationConfig) => Partial<BottomTabNavigationOptions>;
  getDrawerOptions: (config?: RTLNavigationConfig) => Partial<DrawerNavigationOptions>;
  getHeaderOptions: (config?: RTLNavigationConfig) => Partial<StackNavigationOptions>;
  getTransitionPreset: (config?: RTLNavigationConfig) => any;
  getSlideDirection: (direction: 'horizontal' | 'vertical', config?: RTLNavigationConfig) => any;
  getHeaderButtonPosition: (position: 'left' | 'right', config?: RTLNavigationConfig) => 'left' | 'right';
  getTabBarPosition: (position: 'left' | 'right', config?: RTLNavigationConfig) => 'left' | 'right';
  getDrawerPosition: (position: 'left' | 'right', config?: RTLNavigationConfig) => 'left' | 'right';
  createRTLAwareScreenOptions: (
    baseOptions: StackNavigationOptions,
    config?: RTLNavigationConfig
  ) => StackNavigationOptions;
}

export const useRTLNavigation = (globalConfig?: RTLNavigationConfig): RTLNavigationHelpers => {
  const { isRTL: contextIsRTL } = useI18n();

  const helpers = useMemo(() => {
    const isRTL = globalConfig?.isRTL !== undefined ? globalConfig.isRTL : contextIsRTL;

    const getStackOptions = (config?: RTLNavigationConfig): Partial<StackNavigationOptions> => {
      const shouldRespectRTL = config?.respectRTL !== false && globalConfig?.respectRTL !== false;
      const effectiveIsRTL = config?.isRTL !== undefined ? config.isRTL : isRTL;
      
      if (!shouldRespectRTL || !effectiveIsRTL) {
        return {};
      }

      return {
        headerTitleAlign: 'center',
        headerBackTitleVisible: false,
        gestureDirection: 'horizontal-inverted',
        cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS,
        headerStyleInterpolator: HeaderStyleInterpolators.forUIKit,
      };
    };

    const getTabOptions = (config?: RTLNavigationConfig): Partial<BottomTabNavigationOptions> => {
      const shouldRespectRTL = config?.respectRTL !== false && globalConfig?.respectRTL !== false;
      const effectiveIsRTL = config?.isRTL !== undefined ? config.isRTL : isRTL;
      
      if (!shouldRespectRTL || !effectiveIsRTL) {
        return {};
      }

      return {
        tabBarLabelPosition: 'below-icon',
        tabBarActiveTintColor: undefined, // Will use theme colors
        tabBarInactiveTintColor: undefined, // Will use theme colors
      };
    };

    const getDrawerOptions = (config?: RTLNavigationConfig): Partial<DrawerNavigationOptions> => {
      const shouldRespectRTL = config?.respectRTL !== false && globalConfig?.respectRTL !== false;
      const effectiveIsRTL = config?.isRTL !== undefined ? config.isRTL : isRTL;
      
      if (!shouldRespectRTL || !effectiveIsRTL) {
        return {
          drawerPosition: 'left',
        };
      }

      return {
        drawerPosition: 'right',
        gestureHandlerProps: {
          activeOffsetX: [-10, 10],
          failOffsetY: [-5, 5],
        },
      };
    };

    const getHeaderOptions = (config?: RTLNavigationConfig): Partial<StackNavigationOptions> => {
      const shouldRespectRTL = config?.respectRTL !== false && globalConfig?.respectRTL !== false;
      const effectiveIsRTL = config?.isRTL !== undefined ? config.isRTL : isRTL;
      
      if (!shouldRespectRTL || !effectiveIsRTL) {
        return {
          headerTitleAlign: 'left',
        };
      }

      return {
        headerTitleAlign: 'right',
        headerBackTitleVisible: false,
        headerTitleStyle: {
          textAlign: 'right',
        },
      };
    };

    const getTransitionPreset = (config?: RTLNavigationConfig) => {
      const shouldRespectRTL = config?.respectRTL !== false && globalConfig?.respectRTL !== false;
      const effectiveIsRTL = config?.isRTL !== undefined ? config.isRTL : isRTL;
      const enableCustomTransitions = config?.enableCustomTransitions !== false && 
                                    globalConfig?.enableCustomTransitions !== false;
      
      if (!shouldRespectRTL || !effectiveIsRTL || !enableCustomTransitions) {
        return TransitionPresets.SlideFromRightIOS;
      }

      // For RTL, we want to slide from left instead of right
      return {
        ...TransitionPresets.SlideFromRightIOS,
        gestureDirection: 'horizontal-inverted',
        cardStyleInterpolator: ({ current, layouts }: any) => {
          return {
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [-layouts.screen.width, 0],
                  }),
                },
              ],
            },
          };
        },
      };
    };

    const getSlideDirection = (
      direction: 'horizontal' | 'vertical',
      config?: RTLNavigationConfig
    ) => {
      const shouldRespectRTL = config?.respectRTL !== false && globalConfig?.respectRTL !== false;
      const effectiveIsRTL = config?.isRTL !== undefined ? config.isRTL : isRTL;
      
      if (direction === 'vertical' || !shouldRespectRTL || !effectiveIsRTL) {
        return direction === 'horizontal' ? 'horizontal' : 'vertical';
      }

      return 'horizontal-inverted';
    };

    const getHeaderButtonPosition = (
      position: 'left' | 'right',
      config?: RTLNavigationConfig
    ): 'left' | 'right' => {
      const shouldRespectRTL = config?.respectRTL !== false && globalConfig?.respectRTL !== false;
      const effectiveIsRTL = config?.isRTL !== undefined ? config.isRTL : isRTL;
      
      if (!shouldRespectRTL || !effectiveIsRTL) {
        return position;
      }

      return position === 'left' ? 'right' : 'left';
    };

    const getTabBarPosition = (
      position: 'left' | 'right',
      config?: RTLNavigationConfig
    ): 'left' | 'right' => {
      const shouldRespectRTL = config?.respectRTL !== false && globalConfig?.respectRTL !== false;
      const effectiveIsRTL = config?.isRTL !== undefined ? config.isRTL : isRTL;
      
      if (!shouldRespectRTL || !effectiveIsRTL) {
        return position;
      }

      return position === 'left' ? 'right' : 'left';
    };

    const getDrawerPosition = (
      position: 'left' | 'right',
      config?: RTLNavigationConfig
    ): 'left' | 'right' => {
      const shouldRespectRTL = config?.respectRTL !== false && globalConfig?.respectRTL !== false;
      const effectiveIsRTL = config?.isRTL !== undefined ? config.isRTL : isRTL;
      
      if (!shouldRespectRTL || !effectiveIsRTL) {
        return position;
      }

      return position === 'left' ? 'right' : 'left';
    };

    const createRTLAwareScreenOptions = (
      baseOptions: StackNavigationOptions,
      config?: RTLNavigationConfig
    ): StackNavigationOptions => {
      const shouldRespectRTL = config?.respectRTL !== false && globalConfig?.respectRTL !== false;
      const effectiveIsRTL = config?.isRTL !== undefined ? config.isRTL : isRTL;
      
      if (!shouldRespectRTL || !effectiveIsRTL) {
        return baseOptions;
      }

      const rtlOptions: StackNavigationOptions = {
        ...baseOptions,
      };

      // Swap header left and right components
      if (baseOptions.headerLeft && !baseOptions.headerRight) {
        rtlOptions.headerRight = baseOptions.headerLeft;
        rtlOptions.headerLeft = undefined;
      } else if (baseOptions.headerRight && !baseOptions.headerLeft) {
        rtlOptions.headerLeft = baseOptions.headerRight;
        rtlOptions.headerRight = undefined;
      }

      // Adjust header title alignment
      if (baseOptions.headerTitleAlign === 'left') {
        rtlOptions.headerTitleAlign = 'right';
      } else if (baseOptions.headerTitleAlign === 'right') {
        rtlOptions.headerTitleAlign = 'left';
      }

      // Apply RTL-aware transition
      if (config?.enableCustomTransitions !== false && globalConfig?.enableCustomTransitions !== false) {
        rtlOptions.gestureDirection = 'horizontal-inverted';
        rtlOptions.cardStyleInterpolator = ({ current, layouts }: any) => {
          return {
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [-layouts.screen.width, 0],
                  }),
                },
              ],
            },
          };
        };
      }

      return rtlOptions;
    };

    return {
      isRTL,
      getStackOptions,
      getTabOptions,
      getDrawerOptions,
      getHeaderOptions,
      getTransitionPreset,
      getSlideDirection,
      getHeaderButtonPosition,
      getTabBarPosition,
      getDrawerPosition,
      createRTLAwareScreenOptions,
    };
  }, [contextIsRTL, globalConfig]);

  return helpers;
};
