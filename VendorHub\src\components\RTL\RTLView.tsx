import React, { useMemo } from 'react';
import { View, ViewProps, StyleSheet, ViewStyle } from 'react-native';
import { useI18n } from '../../hooks/useI18n';

interface RTLViewProps extends ViewProps {
  style?: ViewStyle | ViewStyle[] | null | undefined;
  children?: React.ReactNode;
  disableRTL?: boolean; // Option to disable RTL transformation for specific cases
}

export const RTLView: React.FC<RTLViewProps> = ({
  style,
  children,
  disableRTL = false,
  ...props
}) => {
  const { isRTL } = useI18n();

  const rtlStyle = useMemo(() => {
    if (!style || disableRTL) return style;

    // Create RTL version of the style
    const flattenedStyle = StyleSheet.flatten(style);
    const rtlFlattenedStyle = { ...flattenedStyle };

    // Only apply RTL transformations if we're in RTL mode
    if (isRTL) {
      // Flip flexDirection
      if (flattenedStyle.flexDirection === 'row') {
        rtlFlattenedStyle.flexDirection = 'row-reverse';
      } else if (flattenedStyle.flexDirection === 'row-reverse') {
        rtlFlattenedStyle.flexDirection = 'row';
      }

      // Convert left/right properties to start/end for better RTL support
      // This allows React Native to handle RTL automatically
      if (flattenedStyle.paddingLeft !== undefined) {
        rtlFlattenedStyle.paddingStart = flattenedStyle.paddingLeft;
        delete rtlFlattenedStyle.paddingLeft;
      }
      if (flattenedStyle.paddingRight !== undefined) {
        rtlFlattenedStyle.paddingEnd = flattenedStyle.paddingRight;
        delete rtlFlattenedStyle.paddingRight;
      }

      if (flattenedStyle.marginLeft !== undefined) {
        rtlFlattenedStyle.marginStart = flattenedStyle.marginLeft;
        delete rtlFlattenedStyle.marginLeft;
      }
      if (flattenedStyle.marginRight !== undefined) {
        rtlFlattenedStyle.marginEnd = flattenedStyle.marginRight;
        delete rtlFlattenedStyle.marginRight;
      }

      // Handle border properties
      if (flattenedStyle.borderLeftWidth !== undefined) {
        rtlFlattenedStyle.borderStartWidth = flattenedStyle.borderLeftWidth;
        delete rtlFlattenedStyle.borderLeftWidth;
      }
      if (flattenedStyle.borderRightWidth !== undefined) {
        rtlFlattenedStyle.borderEndWidth = flattenedStyle.borderRightWidth;
        delete rtlFlattenedStyle.borderRightWidth;
      }

      if (flattenedStyle.borderLeftColor !== undefined) {
        rtlFlattenedStyle.borderStartColor = flattenedStyle.borderLeftColor;
        delete rtlFlattenedStyle.borderLeftColor;
      }
      if (flattenedStyle.borderRightColor !== undefined) {
        rtlFlattenedStyle.borderEndColor = flattenedStyle.borderRightColor;
        delete rtlFlattenedStyle.borderRightColor;
      }

      // For absolute positioning, we still need to flip left/right
      if (flattenedStyle.left !== undefined && flattenedStyle.right === undefined) {
        rtlFlattenedStyle.right = flattenedStyle.left;
        delete rtlFlattenedStyle.left;
      } else if (flattenedStyle.right !== undefined && flattenedStyle.left === undefined) {
        rtlFlattenedStyle.left = flattenedStyle.right;
        delete rtlFlattenedStyle.right;
      }
    }
    
    // Flip border radius for asymmetric corners
    if (flattenedStyle.borderTopLeftRadius !== undefined) {
      rtlFlattenedStyle.borderTopRightRadius = flattenedStyle.borderTopLeftRadius;
      delete rtlFlattenedStyle.borderTopLeftRadius;
    }
    if (flattenedStyle.borderTopRightRadius !== undefined) {
      rtlFlattenedStyle.borderTopLeftRadius = flattenedStyle.borderTopRightRadius;
      delete rtlFlattenedStyle.borderTopRightRadius;
    }
    if (flattenedStyle.borderBottomLeftRadius !== undefined) {
      rtlFlattenedStyle.borderBottomRightRadius = flattenedStyle.borderBottomLeftRadius;
      delete rtlFlattenedStyle.borderBottomLeftRadius;
    }
    if (flattenedStyle.borderBottomRightRadius !== undefined) {
      rtlFlattenedStyle.borderBottomLeftRadius = flattenedStyle.borderBottomRightRadius;
      delete rtlFlattenedStyle.borderBottomRightRadius;
    }

    return rtlFlattenedStyle;
  }, [style, isRTL, disableRTL]);

  return <View style={rtlStyle} {...props}>{children}</View>;
};
