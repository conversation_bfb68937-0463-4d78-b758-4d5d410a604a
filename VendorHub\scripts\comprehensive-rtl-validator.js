#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  srcDir: path.join(__dirname, '../src'),
  outputFile: path.join(__dirname, '../COMPREHENSIVE_RTL_VALIDATION_REPORT.md'),
  
  // Validation categories based on the RTL guide
  validationChecks: {
    foundationSetup: {
      title: "🏗️ Foundation Setup",
      checks: [
        "I18nManager configuration",
        "RTL-aware component wrappers",
        "Core RTL utility functions and hooks",
        "Testing infrastructure for both directions"
      ]
    },
    componentMigration: {
      title: "🧩 Component Migration", 
      checks: [
        "All basic React Native components replaced with RTL-aware wrappers",
        "All styles use start/end properties instead of left/right",
        "Conditional styling implemented where start/end properties aren't sufficient",
        "RTL-aware animation utilities created"
      ]
    },
    layoutValidation: {
      title: "📐 Layout Validation",
      checks: [
        "All components tested in both LTR and RTL modes",
        "Icon directionality is correct throughout the app",
        "Navigation flows feel natural in both directions", 
        "Text alignment and input behavior validated"
      ]
    },
    advancedFeatures: {
      title: "🚀 Advanced Features",
      checks: [
        "Horizontal scrolling components work correctly",
        "Accessibility works correctly in both directions",
        "RTL-aware gesture handling implemented",
        "Performance optimized for RTL layouts"
      ]
    },
    qualityAssurance: {
      title: "✅ Quality Assurance",
      checks: [
        "Comprehensive testing with native RTL speakers",
        "Visual regression testing performed",
        "Platform-specific RTL guidelines validated",
        "RTL implementation patterns documented"
      ]
    }
  }
};

/**
 * Scan for RTL implementation completeness
 */
function scanRTLImplementation() {
  const results = {
    rtlComponents: scanRTLComponents(),
    rtlHooks: scanRTLHooks(),
    rtlUtilities: scanRTLUtilities(),
    rtlTesting: scanRTLTesting(),
    styleImplementation: scanStyleImplementation(),
    animationSupport: scanAnimationSupport(),
    navigationSupport: scanNavigationSupport(),
    gestureSupport: scanGestureSupport()
  };
  
  return results;
}

/**
 * Scan for RTL components
 */
function scanRTLComponents() {
  const rtlComponentsDir = path.join(config.srcDir, 'components/RTL');
  const results = {
    found: [],
    missing: [],
    status: 'unknown'
  };
  
  if (!fs.existsSync(rtlComponentsDir)) {
    results.status = 'missing';
    results.missing.push('RTL components directory not found');
    return results;
  }
  
  const expectedComponents = [
    'RTLView.tsx',
    'RTLText.tsx', 
    'RTLIcon.tsx',
    'RTLInput.tsx',
    'RTLScrollView.tsx',
    'RTLSafeAreaView.tsx',
    'RTLFlatList.tsx',
    'RTLSectionList.tsx',
    'RTLTouchableOpacity.tsx',
    'index.ts'
  ];
  
  const files = fs.readdirSync(rtlComponentsDir);
  
  expectedComponents.forEach(component => {
    if (files.includes(component)) {
      results.found.push(component);
    } else {
      results.missing.push(component);
    }
  });
  
  results.status = results.missing.length === 0 ? 'complete' : 'partial';
  return results;
}

/**
 * Scan for RTL hooks
 */
function scanRTLHooks() {
  const hooksDir = path.join(config.srcDir, 'hooks');
  const results = {
    found: [],
    missing: [],
    status: 'unknown'
  };
  
  const expectedHooks = [
    'useRTL.ts',
    'useRTLStyles.ts',
    'useRTLAnimation.ts', 
    'useRTLNavigation.ts',
    'useI18n.ts'
  ];
  
  if (!fs.existsSync(hooksDir)) {
    results.status = 'missing';
    results.missing = expectedHooks;
    return results;
  }
  
  const files = fs.readdirSync(hooksDir);
  
  expectedHooks.forEach(hook => {
    if (files.includes(hook)) {
      results.found.push(hook);
    } else {
      results.missing.push(hook);
    }
  });
  
  results.status = results.missing.length === 0 ? 'complete' : 'partial';
  return results;
}

/**
 * Scan for RTL utilities
 */
function scanRTLUtilities() {
  const utilsDir = path.join(config.srcDir, 'utils');
  const results = {
    found: [],
    missing: [],
    status: 'unknown'
  };
  
  const expectedUtils = [
    'rtlAnimations.ts',
    'rtlTransitions.ts',
    'rtlGestures.ts',
    'rtlTesting.ts'
  ];
  
  if (!fs.existsSync(utilsDir)) {
    results.status = 'missing';
    results.missing = expectedUtils;
    return results;
  }
  
  const files = fs.readdirSync(utilsDir);
  
  expectedUtils.forEach(util => {
    if (files.includes(util)) {
      results.found.push(util);
    } else {
      results.missing.push(util);
    }
  });
  
  results.status = results.missing.length === 0 ? 'complete' : 'partial';
  return results;
}

/**
 * Scan for RTL testing infrastructure
 */
function scanRTLTesting() {
  const results = {
    testingUtils: false,
    validationScripts: false,
    snapshotTests: false,
    status: 'unknown'
  };
  
  // Check for testing utilities
  const testingUtilsPath = path.join(config.srcDir, 'utils/rtlTesting.ts');
  results.testingUtils = fs.existsSync(testingUtilsPath);
  
  // Check for validation scripts
  const scriptsDir = path.join(__dirname);
  const scriptFiles = fs.readdirSync(scriptsDir);
  results.validationScripts = scriptFiles.some(file => file.includes('rtl'));
  
  // Check for snapshot tests (would need to scan test files)
  results.snapshotTests = false; // Placeholder - would need actual test scanning
  
  const completedChecks = [results.testingUtils, results.validationScripts, results.snapshotTests].filter(Boolean).length;
  results.status = completedChecks === 3 ? 'complete' : completedChecks > 0 ? 'partial' : 'missing';
  
  return results;
}

/**
 * Scan style implementation for start/end properties
 */
function scanStyleImplementation() {
  const results = {
    startEndUsage: 0,
    leftRightUsage: 0,
    status: 'unknown'
  };
  
  // This would scan all style files for start/end vs left/right usage
  // Simplified implementation
  results.status = 'partial'; // Placeholder
  return results;
}

/**
 * Scan animation support
 */
function scanAnimationSupport() {
  const results = {
    rtlAnimations: false,
    rtlTransitions: false,
    status: 'unknown'
  };
  
  results.rtlAnimations = fs.existsSync(path.join(config.srcDir, 'utils/rtlAnimations.ts'));
  results.rtlTransitions = fs.existsSync(path.join(config.srcDir, 'utils/rtlTransitions.ts'));
  
  const completedChecks = [results.rtlAnimations, results.rtlTransitions].filter(Boolean).length;
  results.status = completedChecks === 2 ? 'complete' : completedChecks > 0 ? 'partial' : 'missing';
  
  return results;
}

/**
 * Scan navigation support
 */
function scanNavigationSupport() {
  const results = {
    rtlNavigationHook: false,
    rtlNavigationHeader: false,
    status: 'unknown'
  };
  
  results.rtlNavigationHook = fs.existsSync(path.join(config.srcDir, 'hooks/useRTLNavigation.ts'));
  results.rtlNavigationHeader = fs.existsSync(path.join(config.srcDir, 'components/navigation/RTLNavigationHeader.tsx'));
  
  const completedChecks = [results.rtlNavigationHook, results.rtlNavigationHeader].filter(Boolean).length;
  results.status = completedChecks === 2 ? 'complete' : completedChecks > 0 ? 'partial' : 'missing';
  
  return results;
}

/**
 * Scan gesture support
 */
function scanGestureSupport() {
  const results = {
    rtlGestures: false,
    status: 'unknown'
  };
  
  results.rtlGestures = fs.existsSync(path.join(config.srcDir, 'utils/rtlGestures.ts'));
  results.status = results.rtlGestures ? 'complete' : 'missing';
  
  return results;
}

/**
 * Generate comprehensive report
 */
function generateReport(scanResults) {
  let report = `# Comprehensive RTL Implementation Validation Report\n\n`;
  report += `**Generated:** ${new Date().toLocaleString()}\n`;
  report += `**Based on:** RTL Implementation Guide (Best_RTL.md)\n\n`;
  
  // Executive Summary
  report += `## 📊 Executive Summary\n\n`;
  const overallStatus = calculateOverallStatus(scanResults);
  report += `**Overall RTL Implementation Status:** ${getStatusEmoji(overallStatus)} ${overallStatus.toUpperCase()}\n\n`;
  
  // Detailed Results
  report += `## 🔍 Detailed Implementation Status\n\n`;
  
  // RTL Components
  report += `### 🧩 RTL Components\n`;
  report += `**Status:** ${getStatusEmoji(scanResults.rtlComponents.status)} ${scanResults.rtlComponents.status.toUpperCase()}\n`;
  report += `**Found:** ${scanResults.rtlComponents.found.length} components\n`;
  if (scanResults.rtlComponents.found.length > 0) {
    report += `- ${scanResults.rtlComponents.found.join('\n- ')}\n`;
  }
  if (scanResults.rtlComponents.missing.length > 0) {
    report += `**Missing:** ${scanResults.rtlComponents.missing.join(', ')}\n`;
  }
  report += `\n`;
  
  // RTL Hooks
  report += `### 🪝 RTL Hooks\n`;
  report += `**Status:** ${getStatusEmoji(scanResults.rtlHooks.status)} ${scanResults.rtlHooks.status.toUpperCase()}\n`;
  report += `**Found:** ${scanResults.rtlHooks.found.length} hooks\n`;
  if (scanResults.rtlHooks.found.length > 0) {
    report += `- ${scanResults.rtlHooks.found.join('\n- ')}\n`;
  }
  if (scanResults.rtlHooks.missing.length > 0) {
    report += `**Missing:** ${scanResults.rtlHooks.missing.join(', ')}\n`;
  }
  report += `\n`;
  
  // RTL Utilities
  report += `### 🛠️ RTL Utilities\n`;
  report += `**Status:** ${getStatusEmoji(scanResults.rtlUtilities.status)} ${scanResults.rtlUtilities.status.toUpperCase()}\n`;
  report += `**Found:** ${scanResults.rtlUtilities.found.length} utilities\n`;
  if (scanResults.rtlUtilities.found.length > 0) {
    report += `- ${scanResults.rtlUtilities.found.join('\n- ')}\n`;
  }
  if (scanResults.rtlUtilities.missing.length > 0) {
    report += `**Missing:** ${scanResults.rtlUtilities.missing.join(', ')}\n`;
  }
  report += `\n`;
  
  // Implementation Checklist
  report += `## ✅ Implementation Checklist (Based on RTL Guide)\n\n`;
  
  Object.entries(config.validationChecks).forEach(([key, category]) => {
    report += `### ${category.title}\n`;
    category.checks.forEach(check => {
      const status = getCheckStatus(check, scanResults);
      report += `- ${status} ${check}\n`;
    });
    report += `\n`;
  });
  
  // Recommendations
  report += `## 💡 Recommendations\n\n`;
  const recommendations = generateRecommendations(scanResults);
  recommendations.forEach(rec => {
    report += `${rec}\n`;
  });
  
  return report;
}

/**
 * Calculate overall implementation status
 */
function calculateOverallStatus(scanResults) {
  const statuses = Object.values(scanResults).map(result => result.status);
  const completeCount = statuses.filter(status => status === 'complete').length;
  const partialCount = statuses.filter(status => status === 'partial').length;
  
  if (completeCount === statuses.length) return 'complete';
  if (completeCount + partialCount > statuses.length * 0.7) return 'mostly-complete';
  if (partialCount > 0) return 'partial';
  return 'incomplete';
}

/**
 * Get status emoji
 */
function getStatusEmoji(status) {
  switch (status) {
    case 'complete': return '✅';
    case 'mostly-complete': return '🟢';
    case 'partial': return '🟡';
    case 'incomplete': 
    case 'missing': return '🔴';
    default: return '❓';
  }
}

/**
 * Get check status
 */
function getCheckStatus(check, scanResults) {
  // Simplified check status logic
  if (check.includes('component')) {
    return getStatusEmoji(scanResults.rtlComponents.status);
  }
  if (check.includes('hook') || check.includes('utility')) {
    return getStatusEmoji(scanResults.rtlHooks.status);
  }
  if (check.includes('testing')) {
    return getStatusEmoji(scanResults.rtlTesting.status);
  }
  if (check.includes('animation')) {
    return getStatusEmoji(scanResults.animationSupport.status);
  }
  if (check.includes('navigation')) {
    return getStatusEmoji(scanResults.navigationSupport.status);
  }
  return '🟡'; // Default to partial
}

/**
 * Generate recommendations
 */
function generateRecommendations(scanResults) {
  const recommendations = [];
  
  if (scanResults.rtlComponents.status !== 'complete') {
    recommendations.push('1. **Complete RTL Components**: Implement missing RTL component wrappers');
  }
  
  if (scanResults.rtlHooks.status !== 'complete') {
    recommendations.push('2. **Implement RTL Hooks**: Create missing RTL utility hooks for better developer experience');
  }
  
  if (scanResults.rtlTesting.status !== 'complete') {
    recommendations.push('3. **Enhance Testing**: Implement comprehensive RTL testing infrastructure');
  }
  
  if (scanResults.animationSupport.status !== 'complete') {
    recommendations.push('4. **Animation Support**: Complete RTL-aware animation utilities');
  }
  
  recommendations.push('5. **Performance Testing**: Conduct performance testing in both LTR and RTL modes');
  recommendations.push('6. **User Testing**: Test with native Arabic speakers for cultural appropriateness');
  
  return recommendations;
}

// Main execution
console.log('🔍 Starting comprehensive RTL validation...');

try {
  const scanResults = scanRTLImplementation();
  const report = generateReport(scanResults);
  
  fs.writeFileSync(config.outputFile, report);
  console.log(`✅ Comprehensive RTL validation report generated: ${config.outputFile}`);
  
  // Print summary to console
  const overallStatus = calculateOverallStatus(scanResults);
  console.log(`📊 Overall RTL Implementation Status: ${getStatusEmoji(overallStatus)} ${overallStatus.toUpperCase()}`);
  
} catch (error) {
  console.error('❌ Error during RTL validation:', error);
  process.exit(1);
}
