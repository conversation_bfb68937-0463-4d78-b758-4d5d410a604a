import React, { useMemo } from 'react';
import { TouchableOpacity, TouchableOpacityProps, StyleSheet, ViewStyle } from 'react-native';
import { useI18n } from '../../hooks/useI18n';

interface RTLTouchableOpacityProps extends TouchableOpacityProps {
  style?: ViewStyle | ViewStyle[] | null | undefined;
  children?: React.ReactNode;
  disableRTL?: boolean; // Option to disable RTL transformation for specific cases
}

export const RTLTouchableOpacity: React.FC<RTLTouchableOpacityProps> = ({
  style,
  children,
  disableRTL = false,
  ...props
}) => {
  const { isRTL } = useI18n();

  const rtlStyle = useMemo(() => {
    if (!style || disableRTL) return style;

    // Create RTL version of the style
    const flattenedStyle = StyleSheet.flatten(style);
    const rtlFlattenedStyle = { ...flattenedStyle };

    // Only apply RTL transformations if we're in RTL mode
    if (isRTL) {
      // Flip flexDirection
      if (flattenedStyle.flexDirection === 'row') {
        rtlFlattenedStyle.flexDirection = 'row-reverse';
      } else if (flattenedStyle.flexDirection === 'row-reverse') {
        rtlFlattenedStyle.flexDirection = 'row';
      }

      // Convert left/right properties to start/end for better RTL support
      if (flattenedStyle.paddingLeft !== undefined) {
        rtlFlattenedStyle.paddingStart = flattenedStyle.paddingLeft;
        delete rtlFlattenedStyle.paddingLeft;
      }
      if (flattenedStyle.paddingRight !== undefined) {
        rtlFlattenedStyle.paddingEnd = flattenedStyle.paddingRight;
        delete rtlFlattenedStyle.paddingRight;
      }

      if (flattenedStyle.marginLeft !== undefined) {
        rtlFlattenedStyle.marginStart = flattenedStyle.marginLeft;
        delete rtlFlattenedStyle.marginLeft;
      }
      if (flattenedStyle.marginRight !== undefined) {
        rtlFlattenedStyle.marginEnd = flattenedStyle.marginRight;
        delete rtlFlattenedStyle.marginRight;
      }

      // Handle border properties
      if (flattenedStyle.borderLeftWidth !== undefined) {
        rtlFlattenedStyle.borderStartWidth = flattenedStyle.borderLeftWidth;
        delete rtlFlattenedStyle.borderLeftWidth;
      }
      if (flattenedStyle.borderRightWidth !== undefined) {
        rtlFlattenedStyle.borderEndWidth = flattenedStyle.borderRightWidth;
        delete rtlFlattenedStyle.borderRightWidth;
      }

      // For absolute positioning, we still need to flip left/right
      if (flattenedStyle.left !== undefined && flattenedStyle.right === undefined) {
        rtlFlattenedStyle.right = flattenedStyle.left;
        delete rtlFlattenedStyle.left;
      } else if (flattenedStyle.right !== undefined && flattenedStyle.left === undefined) {
        rtlFlattenedStyle.left = flattenedStyle.right;
        delete rtlFlattenedStyle.right;
      }
    }

    return rtlFlattenedStyle;
  }, [style, isRTL, disableRTL]);

  return (
    <TouchableOpacity style={rtlStyle} {...props}>
      {children}
    </TouchableOpacity>
  );
};
