import React, { useMemo } from 'react';
import { Text, TextProps, StyleSheet, TextStyle } from 'react-native';
import { useI18n } from '../../hooks/useI18n';
import { useFont } from '../../hooks/useFont';
import FontService from '../../services/FontService';

interface RTLTextProps extends TextProps {
  style?: TextStyle | TextStyle[] | null | undefined;
  children?: React.ReactNode;
  weight?: 'regular' | 'medium' | 'bold' | 'light';
  adjustFontSize?: boolean; // Whether to apply Arabic font size adjustment
}

export const RTLText: React.FC<RTLTextProps> = ({
  style,
  children,
  weight = 'regular',
  adjustFontSize = true,
  ...props
}) => {
  const { isRTL, currentLanguage } = useI18n();

  // Get appropriate font style for current language
  const fontStyle = useFont({ weight });

  const rtlStyle = useMemo(() => {
    const baseStyle = { ...fontStyle };

    if (!style) {
      // Apply default RTL text alignment if no style provided
      if (isRTL) {
        baseStyle.textAlign = 'right';
      }
      return baseStyle;
    }

    const flattenedStyle = StyleSheet.flatten(style);
    const rtlFlattenedStyle = { ...baseStyle, ...flattenedStyle };

    // Apply RTL text alignment logic
    if (isRTL) {
      // If no textAlign is specified, default to right for RTL
      if (!flattenedStyle.textAlign) {
        rtlFlattenedStyle.textAlign = 'right';
      } else {
        // Flip text alignment if specified and different from default
        if (flattenedStyle.textAlign === 'left') {
          rtlFlattenedStyle.textAlign = 'right';
        } else if (flattenedStyle.textAlign === 'right') {
          rtlFlattenedStyle.textAlign = 'left';
        }
        // Keep 'center' and 'justify' as-is
      }
    } else {
      // For LTR, if no textAlign is specified, default to left
      if (!flattenedStyle.textAlign) {
        rtlFlattenedStyle.textAlign = 'left';
      }
    }

    // Apply Arabic font size adjustment if enabled
    if (adjustFontSize && flattenedStyle.fontSize) {
      rtlFlattenedStyle.fontSize = FontService.getAdjustedFontSize(
        flattenedStyle.fontSize,
        currentLanguage
      );

      // Update line height proportionally if not explicitly set
      if (!flattenedStyle.lineHeight) {
        rtlFlattenedStyle.lineHeight = FontService.getOptimalLineHeight(
          rtlFlattenedStyle.fontSize,
          currentLanguage
        );
      }
    }

    return rtlFlattenedStyle;
  }, [style, isRTL, fontStyle, adjustFontSize, currentLanguage]);

  return <Text style={rtlStyle} {...props}>{children}</Text>;
};
