import React, { useState } from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { useTheme, useI18n } from '../hooks';
import { COLORS } from '../constants/theme';
import { RTLIcon } from '../components/RTL';
import { RTLTabBar, RTLDrawerMenu, RTLNavigationHeader } from '../components/navigation';

import { AdminDashboardScreen } from '../screens/admin/AdminDashboardScreen';
import { VendorManagementScreen } from '../screens/admin/VendorManagementScreen';
import { ProductOverviewScreen } from '../screens/admin/ProductOverviewScreen';
import { OrderManagementScreen } from '../screens/admin/OrderManagementScreen';

export type AdminTabParamList = {
  Dashboard: undefined;
  Vendors: undefined;
  Products: undefined;
  Orders: undefined;
};

export type AdminStackParamList = {
  AdminTabs: undefined;
  VendorDetails: { vendorId: string };
  ProductDetails: { productId: string };
  OrderDetails: { orderId: string };
};

const Tab = createBottomTabNavigator<AdminTabParamList>();
const Stack = createStackNavigator<AdminStackParamList>();

const AdminTabs: React.FC<{ navigation: any }> = ({ navigation }) => {
  const { colors } = useTheme();
  const { t, isRTL } = useI18n();
  const [isDrawerVisible, setIsDrawerVisible] = useState(false);

  const handleHamburgerPress = () => {
    setIsDrawerVisible(true);
  };

  const handleDrawerClose = () => {
    setIsDrawerVisible(false);
  };

  return (
    <>
      <Tab.Navigator
        tabBar={(props) => (
          <RTLTabBar
            {...props}
            useGradient={true}
            showLabels={false}
          />
        )}
        screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Dashboard':
              iconName = focused ? 'analytics' : 'analytics-outline';
              break;
            case 'Vendors':
              iconName = focused ? 'storefront' : 'storefront-outline';
              break;
            case 'Products':
              iconName = focused ? 'cube' : 'cube-outline';
              break;
            case 'Orders':
              iconName = focused ? 'receipt' : 'receipt-outline';
              break;
            default:
              iconName = 'circle-outline';
          }

          return <RTLIcon name={iconName as any} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textSecondary,
        tabBarShowLabel: false,
        header: ({ route, options }) => (
          <RTLNavigationHeader
            showLogo={true}
            minimal={true}
            glassy={true}
            showHamburgerMenu={true}
            onHamburgerPress={handleHamburgerPress}
            showShadow={false}
            height={50}
          />
        ),
      })}
    >
      {isRTL ? (
        // RTL order: Orders, Products, Vendors, Dashboard (so Dashboard appears first from right)
        <>
          <Tab.Screen
            name="Orders"
            component={OrderManagementScreen}
          />
          <Tab.Screen
            name="Products"
            component={ProductOverviewScreen}
          />
          <Tab.Screen
            name="Vendors"
            component={VendorManagementScreen}
          />
          <Tab.Screen
            name="Dashboard"
            component={AdminDashboardScreen}
          />
        </>
      ) : (
        // LTR order: Dashboard, Vendors, Products, Orders (normal left-to-right)
        <>
          <Tab.Screen
            name="Dashboard"
            component={AdminDashboardScreen}
          />
          <Tab.Screen
            name="Vendors"
            component={VendorManagementScreen}
          />
          <Tab.Screen
            name="Products"
            component={ProductOverviewScreen}
          />
          <Tab.Screen
            name="Orders"
            component={OrderManagementScreen}
          />
        </>
      )}
    </Tab.Navigator>

    {/* Drawer Menu */}
    <RTLDrawerMenu
      isVisible={isDrawerVisible}
      onClose={handleDrawerClose}
      navigation={navigation}
    />
  </>
);
};

export const AdminNavigator: React.FC = () => {
  const { colors } = useTheme();
  const { isRTL, t } = useI18n();

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyleInterpolator: ({ current, layouts }) => {
          return {
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [
                      isRTL ? -layouts.screen.width : layouts.screen.width,
                      0
                    ],
                  }),
                },
              ],
            },
          };
        },
      }}
    >
      <Stack.Screen
        name="AdminTabs"
        options={{ headerShown: false }}
      >
        {(props) => <AdminTabs {...props} />}
      </Stack.Screen>
      {/* Additional stack screens will be added here */}
    </Stack.Navigator>
  );
};
