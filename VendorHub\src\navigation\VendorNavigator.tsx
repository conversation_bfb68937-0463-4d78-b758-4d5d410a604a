import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { useTheme, useAuth, useI18n } from '../hooks';
import { RTLIcon } from '../components/RTL';

import { VendorPendingScreen } from '../screens/vendor/VendorPendingScreen';
import { VendorDashboardScreen } from '../screens/vendor/VendorDashboardScreen';
import { VendorProductsScreen } from '../screens/vendor/VendorProductsScreen';
import { VendorOrdersScreen } from '../screens/vendor/VendorOrdersScreen';
import { AddProductScreen } from '../screens/vendor/AddProductScreen';
import { EditProductScreen } from '../screens/vendor/EditProductScreen';
import { ShopSettingsScreen } from '../screens/vendor/ShopSettingsScreen';
import { VendorShopScreen } from '../screens/public/VendorShopScreen';

export type VendorTabParamList = {
  Overview: undefined;
  Products: undefined;
  Orders: undefined;
  Shop: undefined;
};

export type VendorStackParamList = {
  VendorTabs: undefined;
  VendorPending: undefined;
  AddProduct: undefined;
  EditProduct: { productId: string };
  OrderDetails: { orderId: string };
  ShopSettings: undefined;
};

const Tab = createBottomTabNavigator<VendorTabParamList>();
const Stack = createStackNavigator<VendorStackParamList>();

const VendorTabs: React.FC = () => {
  const { colors } = useTheme();
  const { t, isRTL } = useI18n();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Overview':
              iconName = focused ? 'analytics' : 'analytics-outline';
              break;
            case 'Products':
              iconName = focused ? 'cube' : 'cube-outline';
              break;
            case 'Orders':
              iconName = focused ? 'receipt' : 'receipt-outline';
              break;
            case 'Shop':
              iconName = focused ? 'storefront' : 'storefront-outline';
              break;
            default:
              iconName = 'circle-outline';
          }

          return <RTLIcon name={iconName as any} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textSecondary,
        tabBarShowLabel: false,
        tabBarStyle: {
          backgroundColor: colors.surface,
          borderTopColor: colors.border,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        header: ({ route, options }) => (
          <RTLNavigationHeader
            showLogo={true}
            minimal={true}
            glassy={true}
            showHamburgerMenu={true}
            onHamburgerPress={handleHamburgerPress}
            showShadow={false}
            height={50}
          />
        ),
      })}
    >
      {isRTL ? (
        // RTL order: Shop, Orders, Products, Overview (so Overview appears first from right)
        <>
          <Tab.Screen
            name="Shop"
            component={VendorShopScreen as any}
          />
          <Tab.Screen
            name="Orders"
            component={VendorOrdersScreen}
          />
          <Tab.Screen
            name="Products"
            component={VendorProductsScreen}
          />
          <Tab.Screen
            name="Overview"
            component={VendorDashboardScreen}
          />
        </>
      ) : (
        // LTR order: Overview, Products, Orders, Shop (normal left-to-right)
        <>
          <Tab.Screen
            name="Overview"
            component={VendorDashboardScreen}
          />
          <Tab.Screen
            name="Products"
            component={VendorProductsScreen}
          />
          <Tab.Screen
            name="Orders"
            component={VendorOrdersScreen}
          />
          <Tab.Screen
            name="Shop"
            component={VendorShopScreen as any}
          />
        </>
      )}
    </Tab.Navigator>
  );
};

export const VendorNavigator: React.FC = () => {
  const { colors } = useTheme();
  const { isVendorApproved } = useAuth();
  const { t, isRTL } = useI18n();

  // Determine initial screen based on vendor approval status
  const initialRouteName = isVendorApproved() ? 'VendorTabs' : 'VendorPending';

  return (
    <Stack.Navigator
      initialRouteName={initialRouteName}
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.surface,
          borderBottomColor: colors.border,
        },
        headerTintColor: colors.textPrimary,
        headerTitleStyle: {
          fontWeight: '600',
        },
        cardStyleInterpolator: ({ current, layouts }) => {
          return {
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [
                      isRTL ? -layouts.screen.width : layouts.screen.width,
                      0
                    ],
                  }),
                },
              ],
            },
          };
        },
      }}
    >
      <Stack.Screen
        name="VendorPending"
        component={VendorPendingScreen}
        options={{
          title: t('vendor.pendingApproval'),
          headerLeft: () => null,
          gestureEnabled: false,
        }}
      />
      <Stack.Screen
        name="VendorTabs"
        component={VendorTabs}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="AddProduct"
        component={AddProductScreen}
        options={{
          title: t('vendor.addProduct'),
        }}
      />
      <Stack.Screen
        name="EditProduct"
        component={EditProductScreen}
        options={{
          title: t('vendor.editProduct'),
        }}
      />
      <Stack.Screen
        name="ShopSettings"
        component={ShopSettingsScreen}
        options={{
          title: t('vendor.shopSettings'),
        }}
      />
    </Stack.Navigator>
  );
};
