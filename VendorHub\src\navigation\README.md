# VendorHub Navigation Setup

This document explains how to use the navigation system in VendorHub with proper TypeScript types.

## Navigation Structure

The app uses React Navigation v6 with the following structure:

```
AppNavigator (Root)
├── AuthNavigator (Stack)
│   ├── Welcome
│   ├── Login
│   └── Register
├── AdminNavigator (Stack)
│   └── AdminTabs (Bottom Tabs)
│       ├── Dashboard
│       ├── Vendors
│       ├── Products
│       └── Orders
├── VendorNavigator (Stack)
│   ├── VendorPending
│   ├── VendorTabs (Bottom Tabs)
│   │   ├── Overview
│   │   ├── Products
│   │   ├── Orders
│   │   └── Shop
│   ├── AddProduct
│   ├── EditProduct
│   └── ShopSettings
└── CustomerNavigator (Stack)
    ├── CustomerTabs (Bottom Tabs)
    │   ├── Home
    │   ├── Shops
    │   ├── Cart
    │   └── Profile
    ├── ProductDetails
    ├── VendorShop
    ├── Checkout
    ├── OrderHistory
    ├── OrderDetails
    ├── Search
    └── Chat
```

## Using Navigation in Screens

### Method 1: Using Typed Navigation Hooks

```typescript
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useCustomerStackNavigation } from '../navigation';

export const HomeScreen: React.FC = () => {
  const navigation = useCustomerStackNavigation();

  const handleProductPress = (productId: string) => {
    // TypeScript will ensure productId is required
    navigation.navigate('ProductDetails', { productId });
  };

  const handleSearchPress = () => {
    // TypeScript knows this screen doesn't need params
    navigation.navigate('Search');
  };

  return (
    <View>
      <Text>Home Screen</Text>
      <TouchableOpacity onPress={() => handleProductPress('123')}>
        <Text>View Product</Text>
      </TouchableOpacity>
      <TouchableOpacity onPress={handleSearchPress}>
        <Text>Search</Text>
      </TouchableOpacity>
    </View>
  );
};
```

### Method 2: Using Screen Props Types

```typescript
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import type { CustomerStackScreenProps } from '../navigation';

type ProductDetailsScreenProps = CustomerStackScreenProps<'ProductDetails'>;

export const ProductDetailsScreen: React.FC<ProductDetailsScreenProps> = ({ 
  navigation, 
  route 
}) => {
  const { productId } = route.params; // TypeScript knows this exists

  const handleVendorPress = (vendorId: string) => {
    navigation.navigate('VendorShop', { vendorId });
  };

  const handleAddToCart = () => {
    // Navigate back to previous screen
    navigation.goBack();
  };

  return (
    <View>
      <Text>Product Details for {productId}</Text>
      <TouchableOpacity onPress={() => handleVendorPress('vendor123')}>
        <Text>View Vendor Shop</Text>
      </TouchableOpacity>
      <TouchableOpacity onPress={handleAddToCart}>
        <Text>Add to Cart</Text>
      </TouchableOpacity>
    </View>
  );
};
```

### Method 3: Using Route Hooks

```typescript
import React from 'react';
import { View, Text } from 'react-native';
import { useCustomerStackRoute, useCustomerStackNavigation } from '../navigation';

export const OrderDetailsScreen: React.FC = () => {
  const route = useCustomerStackRoute<'OrderDetails'>();
  const navigation = useCustomerStackNavigation();
  
  const { orderId } = route.params; // TypeScript knows this exists

  return (
    <View>
      <Text>Order Details for {orderId}</Text>
    </View>
  );
};
```

## Available Navigation Hooks

### Customer Navigation
- `useCustomerStackNavigation()` - For stack navigation
- `useCustomerTabNavigation()` - For tab navigation
- `useCustomerStackRoute<ScreenName>()` - For accessing route params

### Vendor Navigation
- `useVendorStackNavigation()` - For stack navigation
- `useVendorTabNavigation()` - For tab navigation
- `useVendorStackRoute<ScreenName>()` - For accessing route params

### Admin Navigation
- `useAdminStackNavigation()` - For stack navigation
- `useAdminTabNavigation()` - For tab navigation

### Auth Navigation
- `useAuthStackNavigation()` - For stack navigation
- `useAuthStackRoute<ScreenName>()` - For accessing route params

## Navigation Parameters

Each screen's required parameters are defined in the type system:

```typescript
// Customer Stack Parameters
type CustomerStackParamList = {
  CustomerTabs: undefined;
  ProductDetails: { productId: string };
  VendorShop: { vendorId: string };
  Checkout: undefined;
  OrderHistory: undefined;
  OrderDetails: { orderId: string };
  Search: undefined;
  Chat: { chatId: string; vendorId?: string; vendorName?: string };
};
```

This ensures type safety when navigating between screens and prevents runtime errors from missing or incorrect parameters.
