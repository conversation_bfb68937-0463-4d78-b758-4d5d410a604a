import { useMemo } from 'react';
import { useI18n } from './useI18n';
import { 
  Animated, 
  ViewStyle, 
  TranslateXTransform, 
  RotateTransform, 
  ScaleXTransform 
} from 'react-native';
import { rtlAwareTranslateX, rtlAwareRotation, shouldMirrorInRTL } from '../utils/rtlAnimations';

export interface RTLAnimationConfig {
  respectRTL?: boolean;
  isRTL?: boolean;
  animationType?: string;
}

export interface RTLAnimationHelpers {
  isRTL: boolean;
  createTranslateX: (value: number, config?: RTLAnimationConfig) => TranslateXTransform;
  createRotation: (value: string, config?: RTLAnimationConfig) => RotateTransform;
  createScaleX: (value: number, config?: RTLAnimationConfig) => ScaleXTransform;
  createTransform: (transforms: Animated.WithAnimatedValue<ViewStyle['transform']>, config?: RTLAnimationConfig) => Animated.WithAnimatedValue<ViewStyle['transform']>;
  interpolateX: (
    animValue: Animated.Value | Animated.AnimatedInterpolation<number>,
    inputRange: number[],
    outputRange: number[],
    config?: RTLAnimationConfig
  ) => Animated.AnimatedInterpolation<number>;
  interpolateRotation: (
    animValue: Animated.Value | Animated.AnimatedInterpolation<number>,
    inputRange: number[],
    outputRange: string[],
    config?: RTLAnimationConfig
  ) => Animated.AnimatedInterpolation<string>;
  getSlideAnimation: (
    direction: 'left' | 'right' | 'up' | 'down',
    distance: number,
    config?: RTLAnimationConfig
  ) => {
    transform: Array<
      | TranslateXTransform
      | { translateY: number }
      | RotateTransform
      | ScaleXTransform
    >;
  };
  shouldMirrorAnimation: (animationType: string) => boolean;
}

export const useRTLAnimation = (globalConfig?: RTLAnimationConfig): RTLAnimationHelpers => {
  const { isRTL: contextIsRTL } = useI18n();

  const helpers = useMemo(() => {
    const isRTL = globalConfig?.isRTL !== undefined ? globalConfig.isRTL : contextIsRTL;

    const createTranslateX = (value: number, config?: RTLAnimationConfig): TranslateXTransform => {
      const shouldRespectRTL = config?.respectRTL !== false && globalConfig?.respectRTL !== false;
      const effectiveIsRTL = config?.isRTL !== undefined ? config.isRTL : isRTL;
      
      if (!shouldRespectRTL || !effectiveIsRTL) {
        return { translateX: value };
      }
      
      return { translateX: -value };
    };

    const createRotation = (value: string, config?: RTLAnimationConfig): RotateTransform => {
      const shouldRespectRTL = config?.respectRTL !== false && globalConfig?.respectRTL !== false;
      const effectiveIsRTL = config?.isRTL !== undefined ? config.isRTL : isRTL;
      const animationType = config?.animationType || 'default';
      
      if (!shouldRespectRTL || !effectiveIsRTL || !shouldMirrorAnimation(animationType)) {
        return { rotate: value };
      }
      
      // Extract numeric value and unit
      const match = value.match(/^(-?\d+(?:\.\d+)?)(.+)$/);
      if (!match) return { rotate: value };
      
      const numericValue = parseFloat(match[1]);
      const unit = match[2];
      
      return { rotate: `${-numericValue}${unit}` };
    };

    const createScaleX = (value: number, config?: RTLAnimationConfig): ScaleXTransform => {
      const shouldRespectRTL = config?.respectRTL !== false && globalConfig?.respectRTL !== false;
      const effectiveIsRTL = config?.isRTL !== undefined ? config.isRTL : isRTL;
      const animationType = config?.animationType || 'default';
      
      if (!shouldRespectRTL || !effectiveIsRTL || !shouldMirrorAnimation(animationType)) {
        return { scaleX: value };
      }
      
      return { scaleX: -value };
    };

    const createTransform = (
      transforms: Animated.WithAnimatedValue<ViewStyle['transform']>,
      config?: RTLAnimationConfig
    ): Animated.WithAnimatedValue<ViewStyle['transform']> => {
      const shouldRespectRTL = config?.respectRTL !== false && globalConfig?.respectRTL !== false;
      const effectiveIsRTL = config?.isRTL !== undefined ? config.isRTL : isRTL;
      const animationType = config?.animationType || 'default';
      
      if (!shouldRespectRTL || !effectiveIsRTL || !transforms) {
        return transforms;
      }
      
      return transforms.map(transform => {
        if ('translateX' in transform && shouldMirrorAnimation(animationType)) {
          return { translateX: transform.translateX instanceof Animated.Value || 
                            transform.translateX instanceof Animated.AnimatedInterpolation
            ? Animated.multiply(transform.translateX, -1)
            : -transform.translateX
          };
        }
        
        if ('rotate' in transform && shouldMirrorAnimation(animationType)) {
          // For animated rotations, we need to handle differently
          if (transform.rotate instanceof Animated.Value || 
              transform.rotate instanceof Animated.AnimatedInterpolation) {
            // This is complex - we'd need to extract the interpolation and reverse it
            // For simplicity, we'll just return the original for now
            return transform;
          }
          
          // For static rotations, we can reverse the angle
          const match = (transform.rotate as string).match(/^(-?\d+(?:\.\d+)?)(.+)$/);
          if (!match) return transform;
          
          const numericValue = parseFloat(match[1]);
          const unit = match[2];
          
          return { rotate: `${-numericValue}${unit}` };
        }
        
        if ('scaleX' in transform && shouldMirrorAnimation(animationType)) {
          return { scaleX: transform.scaleX instanceof Animated.Value || 
                          transform.scaleX instanceof Animated.AnimatedInterpolation
            ? Animated.multiply(transform.scaleX, -1)
            : -transform.scaleX
          };
        }
        
        return transform;
      }) as Animated.WithAnimatedValue<ViewStyle['transform']>;
    };

    const interpolateX = (
      animValue: Animated.Value | Animated.AnimatedInterpolation<number>,
      inputRange: number[],
      outputRange: number[],
      config?: RTLAnimationConfig
    ): Animated.AnimatedInterpolation<number> => {
      const shouldRespectRTL = config?.respectRTL !== false && globalConfig?.respectRTL !== false;
      const effectiveIsRTL = config?.isRTL !== undefined ? config.isRTL : isRTL;
      const animationType = config?.animationType || 'default';
      
      if (!shouldRespectRTL || !effectiveIsRTL || !shouldMirrorAnimation(animationType)) {
        return animValue.interpolate({
          inputRange,
          outputRange,
          extrapolate: 'clamp',
        });
      }
      
      // Reverse output range for RTL
      const rtlOutputRange = [...outputRange].map(value => -value);
      
      return animValue.interpolate({
        inputRange,
        outputRange: rtlOutputRange,
        extrapolate: 'clamp',
      });
    };

    const interpolateRotation = (
      animValue: Animated.Value | Animated.AnimatedInterpolation<number>,
      inputRange: number[],
      outputRange: string[],
      config?: RTLAnimationConfig
    ): Animated.AnimatedInterpolation<string> => {
      const shouldRespectRTL = config?.respectRTL !== false && globalConfig?.respectRTL !== false;
      const effectiveIsRTL = config?.isRTL !== undefined ? config.isRTL : isRTL;
      const animationType = config?.animationType || 'default';
      
      if (!shouldRespectRTL || !effectiveIsRTL || !shouldMirrorAnimation(animationType)) {
        return animValue.interpolate({
          inputRange,
          outputRange,
          extrapolate: 'clamp',
        });
      }
      
      // Reverse rotation angles for RTL
      const rtlOutputRange = outputRange.map(value => {
        const match = value.match(/^(-?\d+(?:\.\d+)?)(.+)$/);
        if (!match) return value;
        
        const numericValue = parseFloat(match[1]);
        const unit = match[2];
        
        return `${-numericValue}${unit}`;
      });
      
      return animValue.interpolate({
        inputRange,
        outputRange: rtlOutputRange,
        extrapolate: 'clamp',
      });
    };

    const getSlideAnimation = (
      direction: 'left' | 'right' | 'up' | 'down',
      distance: number,
      config?: RTLAnimationConfig
    ) => {
      const shouldRespectRTL = config?.respectRTL !== false && globalConfig?.respectRTL !== false;
      const effectiveIsRTL = config?.isRTL !== undefined ? config.isRTL : isRTL;
      
      if (!shouldRespectRTL || !effectiveIsRTL) {
        switch (direction) {
          case 'left':
            return { transform: [{ translateX: -distance }] };
          case 'right':
            return { transform: [{ translateX: distance }] };
          case 'up':
            return { transform: [{ translateY: -distance }] };
          case 'down':
            return { transform: [{ translateY: distance }] };
        }
      }
      
      // In RTL mode, reverse horizontal directions
      switch (direction) {
        case 'left':
          return { transform: [{ translateX: distance }] };
        case 'right':
          return { transform: [{ translateX: -distance }] };
        case 'up':
          return { transform: [{ translateY: -distance }] };
        case 'down':
          return { transform: [{ translateY: distance }] };
      }
    };

    const shouldMirrorAnimation = (animationType: string): boolean => {
      const nonMirroredAnimations = [
        'spin',
        'rotate',
        'pulse',
        'scale',
        'fade',
        'bounce-vertical',
        'loading',
      ];
      
      return !nonMirroredAnimations.includes(animationType);
    };

    return {
      isRTL,
      createTranslateX,
      createRotation,
      createScaleX,
      createTransform,
      interpolateX,
      interpolateRotation,
      getSlideAnimation,
      shouldMirrorAnimation,
    };
  }, [contextIsRTL, globalConfig]);

  return helpers;
};
