import { I18nManager } from 'react-native';
import { ReactTestInstance } from 'react-test-renderer';

export interface RTLTestConfig {
  testBothDirections?: boolean;
  validateTextAlignment?: boolean;
  validateFlexDirection?: boolean;
  validateMarginPadding?: boolean;
  validateIconMirroring?: boolean;
  validateScrollDirection?: boolean;
}

export interface RTLTestResult {
  passed: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

/**
 * RTL Testing Utilities for React Native Components
 */
export class RTLTester {
  private config: RTLTestConfig;

  constructor(config: RTLTestConfig = {}) {
    this.config = {
      testBothDirections: true,
      validateTextAlignment: true,
      validateFlexDirection: true,
      validateMarginPadding: true,
      validateIconMirroring: true,
      validateScrollDirection: true,
      ...config,
    };
  }

  /**
   * Test a component in both LTR and RTL modes
   */
  public async testComponentInBothDirections<T>(
    renderComponent: (isRTL: boolean) => T,
    testFunction: (component: T, isRTL: boolean) => RTLTestResult
  ): Promise<{ ltr: RTLTestResult; rtl: RTLTestResult }> {
    if (!this.config.testBothDirections) {
      const currentRTL = I18nManager.isRTL;
      const component = renderComponent(currentRTL);
      const result = testFunction(component, currentRTL);
      return currentRTL ? { ltr: this.createEmptyResult(), rtl: result } : { ltr: result, rtl: this.createEmptyResult() };
    }

    // Test LTR mode
    I18nManager.forceRTL(false);
    const ltrComponent = renderComponent(false);
    const ltrResult = testFunction(ltrComponent, false);

    // Test RTL mode
    I18nManager.forceRTL(true);
    const rtlComponent = renderComponent(true);
    const rtlResult = testFunction(rtlComponent, true);

    return { ltr: ltrResult, rtl: rtlResult };
  }

  /**
   * Validate text alignment in RTL components
   */
  public validateTextAlignment(component: ReactTestInstance, isRTL: boolean): RTLTestResult {
    const result: RTLTestResult = {
      passed: true,
      errors: [],
      warnings: [],
      suggestions: [],
    };

    if (!this.config.validateTextAlignment) {
      return result;
    }

    try {
      // Find all text components
      const textComponents = this.findComponentsByType(component, 'Text');
      
      textComponents.forEach((textComponent, index) => {
        const style = this.getComponentStyle(textComponent);
        
        if (style && style.textAlign) {
          const expectedAlign = isRTL ? 'right' : 'left';
          const actualAlign = style.textAlign;
          
          if (actualAlign === 'left' && isRTL) {
            result.warnings.push(`Text component ${index} uses left alignment in RTL mode`);
          } else if (actualAlign === 'right' && !isRTL) {
            result.warnings.push(`Text component ${index} uses right alignment in LTR mode`);
          }
        } else if (isRTL) {
          result.suggestions.push(`Text component ${index} should explicitly set textAlign for RTL support`);
        }
      });
    } catch (error) {
      result.errors.push(`Error validating text alignment: ${error}`);
      result.passed = false;
    }

    return result;
  }

  /**
   * Validate flex direction in RTL components
   */
  public validateFlexDirection(component: ReactTestInstance, isRTL: boolean): RTLTestResult {
    const result: RTLTestResult = {
      passed: true,
      errors: [],
      warnings: [],
      suggestions: [],
    };

    if (!this.config.validateFlexDirection) {
      return result;
    }

    try {
      const viewComponents = this.findComponentsByType(component, 'View');
      
      viewComponents.forEach((viewComponent, index) => {
        const style = this.getComponentStyle(viewComponent);
        
        if (style && style.flexDirection === 'row') {
          if (isRTL) {
            result.suggestions.push(`View component ${index} with flexDirection 'row' should consider 'row-reverse' in RTL mode`);
          }
        }
      });
    } catch (error) {
      result.errors.push(`Error validating flex direction: ${error}`);
      result.passed = false;
    }

    return result;
  }

  /**
   * Validate margin and padding properties
   */
  public validateMarginPadding(component: ReactTestInstance, isRTL: boolean): RTLTestResult {
    const result: RTLTestResult = {
      passed: true,
      errors: [],
      warnings: [],
      suggestions: [],
    };

    if (!this.config.validateMarginPadding) {
      return result;
    }

    try {
      const allComponents = this.findAllComponents(component);
      
      allComponents.forEach((comp, index) => {
        const style = this.getComponentStyle(comp);
        
        if (style) {
          // Check for hardcoded left/right properties
          const hasLeftRight = this.hasLeftRightProperties(style);
          const hasStartEnd = this.hasStartEndProperties(style);
          
          if (hasLeftRight && !hasStartEnd) {
            result.warnings.push(`Component ${index} uses left/right properties instead of start/end for better RTL support`);
          }
          
          if (hasLeftRight && hasStartEnd) {
            result.errors.push(`Component ${index} has both left/right and start/end properties which may cause conflicts`);
            result.passed = false;
          }
        }
      });
    } catch (error) {
      result.errors.push(`Error validating margin/padding: ${error}`);
      result.passed = false;
    }

    return result;
  }

  /**
   * Validate icon mirroring in RTL mode
   */
  public validateIconMirroring(component: ReactTestInstance, isRTL: boolean): RTLTestResult {
    const result: RTLTestResult = {
      passed: true,
      errors: [],
      warnings: [],
      suggestions: [],
    };

    if (!this.config.validateIconMirroring) {
      return result;
    }

    try {
      // This would need to be customized based on your icon implementation
      const iconComponents = this.findComponentsByDisplayName(component, /Icon|RTLIcon/);
      
      iconComponents.forEach((iconComponent, index) => {
        const props = iconComponent.props;
        
        if (props && props.name) {
          const iconName = props.name;
          const isDirectionalIcon = this.isDirectionalIcon(iconName);
          
          if (isDirectionalIcon && isRTL) {
            const hasRTLHandling = props.rtl !== undefined || props.mirror !== undefined;
            
            if (!hasRTLHandling) {
              result.warnings.push(`Directional icon "${iconName}" at index ${index} may need RTL mirroring`);
            }
          }
        }
      });
    } catch (error) {
      result.errors.push(`Error validating icon mirroring: ${error}`);
      result.passed = false;
    }

    return result;
  }

  /**
   * Validate scroll direction in horizontal scrollable components
   */
  public validateScrollDirection(component: ReactTestInstance, isRTL: boolean): RTLTestResult {
    const result: RTLTestResult = {
      passed: true,
      errors: [],
      warnings: [],
      suggestions: [],
    };

    if (!this.config.validateScrollDirection) {
      return result;
    }

    try {
      const scrollComponents = this.findComponentsByType(component, ['ScrollView', 'FlatList']);
      
      scrollComponents.forEach((scrollComponent, index) => {
        const props = scrollComponent.props;
        
        if (props && props.horizontal) {
          if (isRTL && !props.inverted && !props.rtlAware) {
            result.suggestions.push(`Horizontal scroll component ${index} should consider RTL-aware scrolling in RTL mode`);
          }
        }
      });
    } catch (error) {
      result.errors.push(`Error validating scroll direction: ${error}`);
      result.passed = false;
    }

    return result;
  }

  /**
   * Run comprehensive RTL validation
   */
  public validateComponent(component: ReactTestInstance, isRTL: boolean): RTLTestResult {
    const results = [
      this.validateTextAlignment(component, isRTL),
      this.validateFlexDirection(component, isRTL),
      this.validateMarginPadding(component, isRTL),
      this.validateIconMirroring(component, isRTL),
      this.validateScrollDirection(component, isRTL),
    ];

    return this.combineResults(results);
  }

  // Helper methods
  private createEmptyResult(): RTLTestResult {
    return { passed: true, errors: [], warnings: [], suggestions: [] };
  }

  private findComponentsByType(component: ReactTestInstance, type: string | string[]): ReactTestInstance[] {
    const types = Array.isArray(type) ? type : [type];
    const found: ReactTestInstance[] = [];
    
    const search = (comp: ReactTestInstance) => {
      if (types.includes(comp.type as string)) {
        found.push(comp);
      }
      comp.children.forEach(child => {
        if (typeof child === 'object' && 'type' in child) {
          search(child as ReactTestInstance);
        }
      });
    };
    
    search(component);
    return found;
  }

  private findComponentsByDisplayName(component: ReactTestInstance, pattern: RegExp): ReactTestInstance[] {
    const found: ReactTestInstance[] = [];
    
    const search = (comp: ReactTestInstance) => {
      const displayName = (comp.type as any)?.displayName || comp.type;
      if (pattern.test(displayName)) {
        found.push(comp);
      }
      comp.children.forEach(child => {
        if (typeof child === 'object' && 'type' in child) {
          search(child as ReactTestInstance);
        }
      });
    };
    
    search(component);
    return found;
  }

  private findAllComponents(component: ReactTestInstance): ReactTestInstance[] {
    const found: ReactTestInstance[] = [component];
    
    const search = (comp: ReactTestInstance) => {
      comp.children.forEach(child => {
        if (typeof child === 'object' && 'type' in child) {
          found.push(child as ReactTestInstance);
          search(child as ReactTestInstance);
        }
      });
    };
    
    search(component);
    return found;
  }

  private getComponentStyle(component: ReactTestInstance): any {
    return component.props?.style;
  }

  private hasLeftRightProperties(style: any): boolean {
    return !!(style.marginLeft || style.marginRight || style.paddingLeft || style.paddingRight ||
              style.borderLeftWidth || style.borderRightWidth || style.left || style.right);
  }

  private hasStartEndProperties(style: any): boolean {
    return !!(style.marginStart || style.marginEnd || style.paddingStart || style.paddingEnd ||
              style.borderStartWidth || style.borderEndWidth);
  }

  private isDirectionalIcon(iconName: string): boolean {
    const directionalIcons = [
      'arrow', 'chevron', 'caret', 'triangle', 'play', 'forward', 'back',
      'next', 'previous', 'skip', 'rewind', 'fast-forward'
    ];
    
    return directionalIcons.some(dir => iconName.toLowerCase().includes(dir));
  }

  private combineResults(results: RTLTestResult[]): RTLTestResult {
    const combined: RTLTestResult = {
      passed: true,
      errors: [],
      warnings: [],
      suggestions: [],
    };

    results.forEach(result => {
      if (!result.passed) {
        combined.passed = false;
      }
      combined.errors.push(...result.errors);
      combined.warnings.push(...result.warnings);
      combined.suggestions.push(...result.suggestions);
    });

    return combined;
  }
}

/**
 * Create a snapshot test for both LTR and RTL modes
 */
export const createRTLSnapshot = (componentName: string, renderComponent: (isRTL: boolean) => any) => {
  return {
    [`${componentName} - LTR`]: () => {
      I18nManager.forceRTL(false);
      return renderComponent(false);
    },
    [`${componentName} - RTL`]: () => {
      I18nManager.forceRTL(true);
      return renderComponent(true);
    },
  };
};
