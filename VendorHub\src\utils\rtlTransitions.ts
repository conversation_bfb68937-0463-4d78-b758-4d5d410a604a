import { Animated, Dimensions, I18nManager } from 'react-native';
import { 
  StackCardInterpolationProps,
  StackCardInterpolatedStyle,
  TransitionSpec,
} from '@react-navigation/stack';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export interface RTLTransitionConfig {
  respectRTL?: boolean;
  isRTL?: boolean;
  duration?: number;
  easing?: (value: number) => number;
}

/**
 * Get the current RTL status
 */
export const isRTL = (): boolean => {
  return I18nManager.isRTL;
};

/**
 * RTL-aware horizontal slide transition
 * In RTL mode, "forward" navigation slides from left to right
 * In LTR mode, "forward" navigation slides from right to left
 */
export const rtlAwareHorizontalSlide = (
  config: RTLTransitionConfig = {}
): ((props: StackCardInterpolationProps) => StackCardInterpolatedStyle) => {
  return ({ current, next, layouts }: StackCardInterpolationProps): StackCardInterpolatedStyle => {
    const shouldRespectRTL = config.respectRTL !== false;
    const effectiveIsRTL = config.isRTL !== undefined ? config.isRTL : isRTL();
    
    const translateX = current.progress.interpolate({
      inputRange: [0, 1],
      outputRange: shouldRespectRTL && effectiveIsRTL 
        ? [-layouts.screen.width, 0]  // RTL: slide from left
        : [layouts.screen.width, 0],  // LTR: slide from right
      extrapolate: 'clamp',
    });

    const nextTranslateX = next
      ? next.progress.interpolate({
          inputRange: [0, 1],
          outputRange: [0, shouldRespectRTL && effectiveIsRTL 
            ? layouts.screen.width   // RTL: slide to right
            : -layouts.screen.width  // LTR: slide to left
          ],
          extrapolate: 'clamp',
        })
      : 0;

    return {
      cardStyle: {
        transform: [{ translateX }],
      },
      overlayStyle: {
        opacity: current.progress.interpolate({
          inputRange: [0, 1],
          outputRange: [0, 0.5],
          extrapolate: 'clamp',
        }),
      },
    };
  };
};

/**
 * RTL-aware modal presentation
 * Slides up from bottom, but respects RTL for any horizontal elements
 */
export const rtlAwareModalSlide = (
  config: RTLTransitionConfig = {}
): ((props: StackCardInterpolationProps) => StackCardInterpolatedStyle) => {
  return ({ current, layouts }: StackCardInterpolationProps): StackCardInterpolatedStyle => {
    const translateY = current.progress.interpolate({
      inputRange: [0, 1],
      outputRange: [layouts.screen.height, 0],
      extrapolate: 'clamp',
    });

    const scale = current.progress.interpolate({
      inputRange: [0, 1],
      outputRange: [0.9, 1],
      extrapolate: 'clamp',
    });

    return {
      cardStyle: {
        transform: [{ translateY }, { scale }],
      },
      overlayStyle: {
        opacity: current.progress.interpolate({
          inputRange: [0, 1],
          outputRange: [0, 0.5],
          extrapolate: 'clamp',
        }),
      },
    };
  };
};

/**
 * RTL-aware fade transition
 */
export const rtlAwareFade = (
  config: RTLTransitionConfig = {}
): ((props: StackCardInterpolationProps) => StackCardInterpolatedStyle) => {
  return ({ current }: StackCardInterpolationProps): StackCardInterpolatedStyle => {
    return {
      cardStyle: {
        opacity: current.progress,
      },
    };
  };
};

/**
 * RTL-aware scale transition
 */
export const rtlAwareScale = (
  config: RTLTransitionConfig = {}
): ((props: StackCardInterpolationProps) => StackCardInterpolatedStyle) => {
  return ({ current }: StackCardInterpolationProps): StackCardInterpolatedStyle => {
    const scale = current.progress.interpolate({
      inputRange: [0, 1],
      outputRange: [0.8, 1],
      extrapolate: 'clamp',
    });

    return {
      cardStyle: {
        transform: [{ scale }],
        opacity: current.progress,
      },
    };
  };
};

/**
 * RTL-aware drawer slide transition
 * Respects drawer position based on RTL
 */
export const rtlAwareDrawerSlide = (
  config: RTLTransitionConfig = {}
): ((props: StackCardInterpolationProps) => StackCardInterpolatedStyle) => {
  return ({ current, layouts }: StackCardInterpolationProps): StackCardInterpolatedStyle => {
    const shouldRespectRTL = config.respectRTL !== false;
    const effectiveIsRTL = config.isRTL !== undefined ? config.isRTL : isRTL();
    
    const translateX = current.progress.interpolate({
      inputRange: [0, 1],
      outputRange: shouldRespectRTL && effectiveIsRTL 
        ? [layouts.screen.width * 0.8, 0]   // RTL: drawer from right
        : [-layouts.screen.width * 0.8, 0], // LTR: drawer from left
      extrapolate: 'clamp',
    });

    return {
      cardStyle: {
        transform: [{ translateX }],
      },
    };
  };
};

/**
 * Create RTL-aware transition specs
 */
export const createRTLTransitionSpec = (config: RTLTransitionConfig = {}): TransitionSpec => {
  return {
    animation: 'timing',
    config: {
      duration: config.duration || 300,
      easing: config.easing || Animated.Easing.out(Animated.Easing.poly(4)),
      useNativeDriver: true,
    },
  };
};

/**
 * RTL-aware gesture configuration
 */
export const getRTLGestureConfig = (config: RTLTransitionConfig = {}) => {
  const shouldRespectRTL = config.respectRTL !== false;
  const effectiveIsRTL = config.isRTL !== undefined ? config.isRTL : isRTL();
  
  if (!shouldRespectRTL || !effectiveIsRTL) {
    return {
      gestureDirection: 'horizontal' as const,
      gestureResponseDistance: {
        horizontal: 50,
        vertical: 135,
      },
    };
  }

  return {
    gestureDirection: 'horizontal-inverted' as const,
    gestureResponseDistance: {
      horizontal: 50,
      vertical: 135,
    },
  };
};

/**
 * Create RTL-aware animated value for horizontal movements
 */
export const createRTLAnimatedValue = (
  initialValue: number = 0,
  config: RTLTransitionConfig = {}
): Animated.Value => {
  const shouldRespectRTL = config.respectRTL !== false;
  const effectiveIsRTL = config.isRTL !== undefined ? config.isRTL : isRTL();
  
  const adjustedValue = shouldRespectRTL && effectiveIsRTL ? -initialValue : initialValue;
  return new Animated.Value(adjustedValue);
};

/**
 * RTL-aware spring animation
 */
export const createRTLSpringAnimation = (
  animatedValue: Animated.Value,
  toValue: number,
  config: RTLTransitionConfig & {
    tension?: number;
    friction?: number;
    mass?: number;
  } = {}
): Animated.CompositeAnimation => {
  const shouldRespectRTL = config.respectRTL !== false;
  const effectiveIsRTL = config.isRTL !== undefined ? config.isRTL : isRTL();
  
  const adjustedToValue = shouldRespectRTL && effectiveIsRTL ? -toValue : toValue;
  
  return Animated.spring(animatedValue, {
    toValue: adjustedToValue,
    tension: config.tension || 100,
    friction: config.friction || 8,
    mass: config.mass || 1,
    useNativeDriver: true,
  });
};

/**
 * RTL-aware timing animation
 */
export const createRTLTimingAnimation = (
  animatedValue: Animated.Value,
  toValue: number,
  config: RTLTransitionConfig & {
    duration?: number;
    delay?: number;
  } = {}
): Animated.CompositeAnimation => {
  const shouldRespectRTL = config.respectRTL !== false;
  const effectiveIsRTL = config.isRTL !== undefined ? config.isRTL : isRTL();
  
  const adjustedToValue = shouldRespectRTL && effectiveIsRTL ? -toValue : toValue;
  
  return Animated.timing(animatedValue, {
    toValue: adjustedToValue,
    duration: config.duration || 300,
    delay: config.delay || 0,
    easing: config.easing || Animated.Easing.out(Animated.Easing.poly(4)),
    useNativeDriver: true,
  });
};

/**
 * Utility to get screen transition presets for RTL
 */
export const getRTLScreenTransitions = (config: RTLTransitionConfig = {}) => {
  return {
    horizontalSlide: rtlAwareHorizontalSlide(config),
    modalSlide: rtlAwareModalSlide(config),
    fade: rtlAwareFade(config),
    scale: rtlAwareScale(config),
    drawerSlide: rtlAwareDrawerSlide(config),
  };
};
