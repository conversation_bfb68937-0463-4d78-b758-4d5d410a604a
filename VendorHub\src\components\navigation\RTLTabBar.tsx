import React, { useState } from 'react';
import { View, StyleSheet, Dimensions, Platform, Animated } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { useTheme, useI18n } from '../../hooks';
import { RTLView, RTLText, RTLIcon, RTLTouchableOpacity } from '../RTL';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, GRADIENTS, SHADOWS, TAB_BAR_HEIGHT } from '../../constants/theme';

const { width: screenWidth } = Dimensions.get('window');

export interface RTLTabBarProps extends BottomTabBarProps {
  useGradient?: boolean;
  gradientColors?: readonly [string, string, ...string[]];
  showLabels?: boolean;
  showBadges?: boolean;
  activeTabScale?: number;
  tabBarStyle?: any;
}

export const RTLTabBar: React.FC<RTLTabBarProps> = ({
  state,
  descriptors,
  navigation,
  useGradient = true,
  gradientColors,
  showLabels = false,
  showBadges = true,
  activeTabScale = 1.1,
  tabBarStyle,
}) => {
  const { colors, isDark } = useTheme();
  const { isRTL, t } = useI18n();
  const insets = useSafeAreaInsets();

  // Determine gradient colors based on theme
  const defaultGradientColors = gradientColors || (isDark ? GRADIENTS.midnight : GRADIENTS.primary);

  // Calculate tab width (full available space)
  const tabWidth = screenWidth / state.routes.length;



  const renderTabButton = (route: any, index: number) => {
    const { options } = descriptors[route.key];
    const label = options.tabBarLabel !== undefined 
      ? options.tabBarLabel 
      : options.title !== undefined 
      ? options.title 
      : route.name;

    const isFocused = state.index === index;

    const onPress = () => {
      const event = navigation.emit({
        type: 'tabPress',
        target: route.key,
        canPreventDefault: true,
      });

      if (!isFocused && !event.defaultPrevented) {
        navigation.navigate(route.name, route.params);
      }
    };

    const onLongPress = () => {
      navigation.emit({
        type: 'tabLongPress',
        target: route.key,
      });
    };

    // Get icon from options
    const iconName = options.tabBarIcon;
    const badge = options.tabBarBadge;

    // Determine colors
    const iconColor = isFocused 
      ? (useGradient ? colors.textOnPrimary : colors.primary)
      : (useGradient ? colors.textOnPrimary : colors.textSecondary);
    
    const labelColor = isFocused 
      ? (useGradient ? colors.textOnPrimary : colors.primary)
      : (useGradient ? colors.textOnPrimary : colors.textSecondary);

    return (
      <RTLTouchableOpacity
        key={route.key}
        accessibilityRole="button"
        accessibilityState={isFocused ? { selected: true } : {}}
        accessibilityLabel={options.tabBarAccessibilityLabel}
        testID={options.tabBarTestID}
        onPress={onPress}
        onLongPress={onLongPress}
        style={[
          styles.tabButton,
          {
            width: tabWidth,
            transform: [{ scale: isFocused ? activeTabScale : 1 }],
          }
        ]}
        activeOpacity={0.7}
      >
        <RTLView style={styles.tabContent}>
          {/* Icon with badge */}
          <RTLView style={styles.iconContainer}>
            {iconName && (
              <RTLIcon
                name={iconName({ focused: isFocused, color: iconColor, size: 24 }).props.name}
                size={24}
                color={iconColor}
              />
            )}
            {showBadges && badge && (
              <RTLView style={[styles.badge, { backgroundColor: colors.error }]}>
                <RTLText style={styles.badgeText}>
                  {typeof badge === 'number' && badge > 99 ? '99+' : badge}
                </RTLText>
              </RTLView>
            )}
          </RTLView>

          {/* Label */}
          {showLabels && (
            <RTLText
              style={[
                styles.tabLabel,
                {
                  color: labelColor,
                  fontWeight: isFocused ? FONT_WEIGHTS.semibold : FONT_WEIGHTS.regular,
                }
              ]}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {label}
            </RTLText>
          )}
        </RTLView>

        {/* Active indicator */}
        {isFocused && (
          <RTLView style={[
            styles.activeIndicator,
            { backgroundColor: useGradient ? colors.textOnPrimary : colors.primary }
          ]} />
        )}
      </RTLTouchableOpacity>
    );
  };



  const renderContent = () => (
    <RTLView style={[
      styles.container,
      {
        paddingBottom: insets.bottom,
        height: TAB_BAR_HEIGHT + insets.bottom,
      },
      tabBarStyle,
    ]}>
      {/* Tab Container */}
      <RTLView style={styles.tabContainer}>
        {/* Render tabs in RTL order if needed */}
        {isRTL
          ? state.routes.slice().reverse().map((route, index) =>
              renderTabButton(route, state.routes.length - 1 - index)
            )
          : state.routes.map((route, index) =>
              renderTabButton(route, index)
            )
        }
      </RTLView>
    </RTLView>
  );

  if (useGradient) {
    return (
      <LinearGradient
        colors={defaultGradientColors}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={[styles.gradientContainer, SHADOWS.large]}
      >
        {renderContent()}
      </LinearGradient>
    );
  }

  return (
    <View style={[
      styles.solidContainer, 
      { backgroundColor: colors.surface },
      SHADOWS.large
    ]}>
      {renderContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  gradientContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  solidContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  container: {
    width: '100%',
    justifyContent: 'flex-end',
  },
  tabContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.xs,
    paddingTop: SPACING.sm,
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xs,
    position: 'relative',
  },
  tabContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 2,
  },
  tabLabel: {
    fontSize: FONT_SIZES.xs,
    textAlign: 'center',
    marginTop: 2,
  },
  badge: {
    position: 'absolute',
    top: -6,
    right: -8,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: FONT_WEIGHTS.bold,
    textAlign: 'center',
  },
  activeIndicator: {
    position: 'absolute',
    top: 0,
    left: '25%',
    right: '25%',
    height: 3,
    borderRadius: 2,
  },

});
