import { <PERSON>18n<PERSON><PERSON><PERSON>, PanGestureHandler, State } from 'react-native-gesture-handler';
import { Animated } from 'react-native';

export interface RTLGestureConfig {
  respectRTL?: boolean;
  isRTL?: boolean;
  threshold?: number;
  velocityThreshold?: number;
}

export interface RTLGestureEvent {
  nativeEvent: {
    translationX: number;
    translationY: number;
    velocityX: number;
    velocityY: number;
    state: State;
    x: number;
    y: number;
  };
}

/**
 * Get the current RTL status
 */
export const isRTL = (): boolean => {
  return I18nManager.isRTL;
};

/**
 * RTL-aware pan gesture handler
 * Adjusts gesture directions based on RTL mode
 */
export class RTLPanGestureHandler {
  private config: RTLGestureConfig;
  private animatedValue: Animated.Value;
  private onGestureStart?: () => void;
  private onGestureEnd?: (direction: 'left' | 'right' | 'up' | 'down' | 'none') => void;
  private onGestureUpdate?: (translationX: number, translationY: number) => void;

  constructor(
    animatedValue: Animated.Value,
    config: RTLGestureConfig = {},
    callbacks: {
      onGestureStart?: () => void;
      onGestureEnd?: (direction: 'left' | 'right' | 'up' | 'down' | 'none') => void;
      onGestureUpdate?: (translationX: number, translationY: number) => void;
    } = {}
  ) {
    this.config = {
      respectRTL: true,
      threshold: 50,
      velocityThreshold: 500,
      ...config,
    };
    this.animatedValue = animatedValue;
    this.onGestureStart = callbacks.onGestureStart;
    this.onGestureEnd = callbacks.onGestureEnd;
    this.onGestureUpdate = callbacks.onGestureUpdate;
  }

  /**
   * Handle gesture state change
   */
  public handleGestureStateChange = (event: RTLGestureEvent) => {
    const { state, translationX, translationY, velocityX, velocityY } = event.nativeEvent;
    const shouldRespectRTL = this.config.respectRTL !== false;
    const effectiveIsRTL = this.config.isRTL !== undefined ? this.config.isRTL : isRTL();

    switch (state) {
      case State.BEGAN:
        this.onGestureStart?.();
        break;

      case State.ACTIVE:
        const adjustedTranslationX = shouldRespectRTL && effectiveIsRTL ? -translationX : translationX;
        this.onGestureUpdate?.(adjustedTranslationX, translationY);
        break;

      case State.END:
        const direction = this.getGestureDirection(
          translationX,
          translationY,
          velocityX,
          velocityY,
          shouldRespectRTL && effectiveIsRTL
        );
        this.onGestureEnd?.(direction);
        break;

      case State.CANCELLED:
      case State.FAILED:
        this.onGestureEnd?.('none');
        break;
    }
  };

  /**
   * Determine gesture direction based on translation and velocity
   */
  private getGestureDirection(
    translationX: number,
    translationY: number,
    velocityX: number,
    velocityY: number,
    isRTLMode: boolean
  ): 'left' | 'right' | 'up' | 'down' | 'none' {
    const threshold = this.config.threshold!;
    const velocityThreshold = this.config.velocityThreshold!;

    // Check if gesture meets threshold requirements
    const hasSignificantTranslation = 
      Math.abs(translationX) > threshold || Math.abs(translationY) > threshold;
    const hasSignificantVelocity = 
      Math.abs(velocityX) > velocityThreshold || Math.abs(velocityY) > velocityThreshold;

    if (!hasSignificantTranslation && !hasSignificantVelocity) {
      return 'none';
    }

    // Determine primary direction
    const isHorizontal = Math.abs(translationX) > Math.abs(translationY);

    if (isHorizontal) {
      if (isRTLMode) {
        // In RTL mode, positive translationX means left, negative means right
        return translationX > 0 ? 'left' : 'right';
      } else {
        // In LTR mode, positive translationX means right, negative means left
        return translationX > 0 ? 'right' : 'left';
      }
    } else {
      // Vertical gestures are not affected by RTL
      return translationY > 0 ? 'down' : 'up';
    }
  }
}

/**
 * RTL-aware swipe gesture detector
 */
export const createRTLSwipeGesture = (
  config: RTLGestureConfig & {
    onSwipeLeft?: () => void;
    onSwipeRight?: () => void;
    onSwipeUp?: () => void;
    onSwipeDown?: () => void;
  } = {}
) => {
  const shouldRespectRTL = config.respectRTL !== false;
  const effectiveIsRTL = config.isRTL !== undefined ? config.isRTL : isRTL();

  return {
    onGestureEvent: (event: RTLGestureEvent) => {
      const { translationX, translationY, state } = event.nativeEvent;
      
      if (state === State.END) {
        const threshold = config.threshold || 50;
        
        if (Math.abs(translationX) > Math.abs(translationY)) {
          // Horizontal swipe
          if (Math.abs(translationX) > threshold) {
            if (shouldRespectRTL && effectiveIsRTL) {
              // In RTL mode, reverse the logic
              if (translationX > 0) {
                config.onSwipeLeft?.();
              } else {
                config.onSwipeRight?.();
              }
            } else {
              // In LTR mode, use normal logic
              if (translationX > 0) {
                config.onSwipeRight?.();
              } else {
                config.onSwipeLeft?.();
              }
            }
          }
        } else {
          // Vertical swipe
          if (Math.abs(translationY) > threshold) {
            if (translationY > 0) {
              config.onSwipeDown?.();
            } else {
              config.onSwipeUp?.();
            }
          }
        }
      }
    },
  };
};

/**
 * RTL-aware drag gesture handler
 */
export const createRTLDragGesture = (
  animatedValue: Animated.ValueXY,
  config: RTLGestureConfig & {
    onDragStart?: () => void;
    onDragEnd?: () => void;
    constrainToAxis?: 'x' | 'y' | 'none';
  } = {}
) => {
  const shouldRespectRTL = config.respectRTL !== false;
  const effectiveIsRTL = config.isRTL !== undefined ? config.isRTL : isRTL();

  return {
    onGestureEvent: Animated.event(
      [
        {
          nativeEvent: {
            translationX: shouldRespectRTL && effectiveIsRTL 
              ? Animated.multiply(animatedValue.x, -1)
              : animatedValue.x,
            translationY: config.constrainToAxis === 'x' ? 0 : animatedValue.y,
          },
        },
      ],
      { useNativeDriver: false }
    ),
    onHandlerStateChange: (event: RTLGestureEvent) => {
      const { state } = event.nativeEvent;
      
      switch (state) {
        case State.BEGAN:
          config.onDragStart?.();
          break;
        case State.END:
        case State.CANCELLED:
        case State.FAILED:
          config.onDragEnd?.();
          break;
      }
    },
  };
};

/**
 * RTL-aware pinch gesture handler
 * Pinch gestures are generally not affected by RTL, but included for completeness
 */
export const createRTLPinchGesture = (
  scaleValue: Animated.Value,
  config: {
    onPinchStart?: () => void;
    onPinchEnd?: () => void;
    minScale?: number;
    maxScale?: number;
  } = {}
) => {
  return {
    onGestureEvent: Animated.event(
      [{ nativeEvent: { scale: scaleValue } }],
      { useNativeDriver: false }
    ),
    onHandlerStateChange: (event: any) => {
      const { state, scale } = event.nativeEvent;
      
      switch (state) {
        case State.BEGAN:
          config.onPinchStart?.();
          break;
        case State.END:
          // Apply scale constraints
          const minScale = config.minScale || 0.5;
          const maxScale = config.maxScale || 3;
          const constrainedScale = Math.max(minScale, Math.min(maxScale, scale));
          
          Animated.spring(scaleValue, {
            toValue: constrainedScale,
            useNativeDriver: false,
          }).start();
          
          config.onPinchEnd?.();
          break;
        case State.CANCELLED:
        case State.FAILED:
          // Reset to original scale
          Animated.spring(scaleValue, {
            toValue: 1,
            useNativeDriver: false,
          }).start();
          
          config.onPinchEnd?.();
          break;
      }
    },
  };
};

/**
 * Utility to create RTL-aware gesture response areas
 */
export const getRTLGestureResponseArea = (
  side: 'left' | 'right',
  config: RTLGestureConfig = {}
) => {
  const shouldRespectRTL = config.respectRTL !== false;
  const effectiveIsRTL = config.isRTL !== undefined ? config.isRTL : isRTL();
  
  if (!shouldRespectRTL || !effectiveIsRTL) {
    return side;
  }
  
  // In RTL mode, flip the sides
  return side === 'left' ? 'right' : 'left';
};
