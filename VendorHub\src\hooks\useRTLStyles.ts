import { useMemo } from 'react';
import { ViewStyle, TextStyle, ImageStyle, StyleSheet } from 'react-native';
import { useI18n } from './useI18n';

export type RTLStyle = ViewStyle | TextStyle | ImageStyle;
export type RTLStyleArray = RTLStyle | RTLStyle[];

export interface RTLStylesConfig {
  respectRTL?: boolean;
  forceDirection?: 'ltr' | 'rtl';
  preserveOriginal?: boolean;
}

export interface RTLStylesHelpers {
  isRTL: boolean;
  createStyles: <T extends Record<string, RTLStyleArray>>(
    styles: T,
    config?: RTLStylesConfig
  ) => T;
  transformStyle: (style: RTLStyleArray, config?: RTLStylesConfig) => RTLStyleArray;
  conditionalStyle: (
    ltrStyle: RTLStyleArray,
    rtlStyle: RTLStyleArray,
    config?: RTLStylesConfig
  ) => RTLStyleArray;
  mirrorStyle: (style: RTLStyleArray) => RTLStyleArray;
  getDirectionalValue: <T>(ltrValue: T, rtlValue: T) => T;
  createResponsiveStyles: <T extends Record<string, RTLStyleArray>>(
    baseStyles: T,
    rtlOverrides?: Partial<T>,
    config?: RTLStylesConfig
  ) => T;
}

export const useRTLStyles = (globalConfig?: RTLStylesConfig): RTLStylesHelpers => {
  const { isRTL: contextIsRTL } = useI18n();

  const helpers = useMemo(() => {
    const isRTL = globalConfig?.forceDirection 
      ? globalConfig.forceDirection === 'rtl'
      : contextIsRTL;

    const transformSingleStyle = (style: RTLStyle, config?: RTLStylesConfig): RTLStyle => {
      const shouldRespectRTL = config?.respectRTL !== false && globalConfig?.respectRTL !== false;
      const effectiveIsRTL = config?.forceDirection 
        ? config.forceDirection === 'rtl'
        : isRTL;

      if (!shouldRespectRTL || !effectiveIsRTL || !style) return style;

      const transformed = { ...style };

      // Handle flex direction
      if ('flexDirection' in transformed) {
        if (transformed.flexDirection === 'row') {
          transformed.flexDirection = 'row-reverse';
        } else if (transformed.flexDirection === 'row-reverse') {
          transformed.flexDirection = 'row';
        }
      }

      // Handle text alignment
      if ('textAlign' in transformed) {
        if (transformed.textAlign === 'left') {
          transformed.textAlign = 'right';
        } else if (transformed.textAlign === 'right') {
          transformed.textAlign = 'left';
        }
      }

      // Convert left/right properties to start/end for better RTL support
      if ('marginLeft' in transformed || 'marginRight' in transformed) {
        const marginLeft = (transformed as any).marginLeft;
        const marginRight = (transformed as any).marginRight;
        
        if (marginLeft !== undefined) {
          (transformed as any).marginStart = marginLeft;
          delete (transformed as any).marginLeft;
        }
        if (marginRight !== undefined) {
          (transformed as any).marginEnd = marginRight;
          delete (transformed as any).marginRight;
        }
      }

      if ('paddingLeft' in transformed || 'paddingRight' in transformed) {
        const paddingLeft = (transformed as any).paddingLeft;
        const paddingRight = (transformed as any).paddingRight;
        
        if (paddingLeft !== undefined) {
          (transformed as any).paddingStart = paddingLeft;
          delete (transformed as any).paddingLeft;
        }
        if (paddingRight !== undefined) {
          (transformed as any).paddingEnd = paddingRight;
          delete (transformed as any).paddingRight;
        }
      }

      // Handle border properties
      if ('borderLeftWidth' in transformed || 'borderRightWidth' in transformed) {
        const borderLeftWidth = (transformed as any).borderLeftWidth;
        const borderRightWidth = (transformed as any).borderRightWidth;
        
        if (borderLeftWidth !== undefined) {
          (transformed as any).borderStartWidth = borderLeftWidth;
          delete (transformed as any).borderLeftWidth;
        }
        if (borderRightWidth !== undefined) {
          (transformed as any).borderEndWidth = borderRightWidth;
          delete (transformed as any).borderRightWidth;
        }
      }

      // Handle absolute positioning
      if ('left' in transformed || 'right' in transformed) {
        const left = (transformed as any).left;
        const right = (transformed as any).right;
        
        if (left !== undefined && right === undefined) {
          (transformed as any).right = left;
          delete (transformed as any).left;
        } else if (right !== undefined && left === undefined) {
          (transformed as any).left = right;
          delete (transformed as any).right;
        }
      }

      return transformed;
    };

    const transformStyle = (style: RTLStyleArray, config?: RTLStylesConfig): RTLStyleArray => {
      if (!style) return style;

      if (Array.isArray(style)) {
        return style.map(s => transformSingleStyle(s, config));
      }

      return transformSingleStyle(style, config);
    };

    const createStyles = <T extends Record<string, RTLStyleArray>>(
      styles: T,
      config?: RTLStylesConfig
    ): T => {
      const transformedStyles = {} as T;

      Object.keys(styles).forEach(key => {
        transformedStyles[key as keyof T] = transformStyle(styles[key], config);
      });

      return StyleSheet.create(transformedStyles) as T;
    };

    const conditionalStyle = (
      ltrStyle: RTLStyleArray,
      rtlStyle: RTLStyleArray,
      config?: RTLStylesConfig
    ): RTLStyleArray => {
      const effectiveIsRTL = config?.forceDirection 
        ? config.forceDirection === 'rtl'
        : isRTL;

      return effectiveIsRTL ? rtlStyle : ltrStyle;
    };

    const mirrorStyle = (style: RTLStyleArray): RTLStyleArray => {
      if (!style) return style;

      const mirrorSingleStyle = (singleStyle: RTLStyle): RTLStyle => {
        return {
          ...singleStyle,
          transform: [
            ...(singleStyle.transform || []),
            { scaleX: -1 }
          ]
        };
      };

      if (Array.isArray(style)) {
        return style.map(mirrorSingleStyle);
      }

      return mirrorSingleStyle(style);
    };

    const getDirectionalValue = <T>(ltrValue: T, rtlValue: T): T => {
      return isRTL ? rtlValue : ltrValue;
    };

    const createResponsiveStyles = <T extends Record<string, RTLStyleArray>>(
      baseStyles: T,
      rtlOverrides?: Partial<T>,
      config?: RTLStylesConfig
    ): T => {
      const effectiveIsRTL = config?.forceDirection 
        ? config.forceDirection === 'rtl'
        : isRTL;

      if (!effectiveIsRTL || !rtlOverrides) {
        return createStyles(baseStyles, config);
      }

      const mergedStyles = { ...baseStyles };
      
      Object.keys(rtlOverrides).forEach(key => {
        if (rtlOverrides[key as keyof T] !== undefined) {
          mergedStyles[key as keyof T] = rtlOverrides[key as keyof T]!;
        }
      });

      return createStyles(mergedStyles, config);
    };

    return {
      isRTL,
      createStyles,
      transformStyle,
      conditionalStyle,
      mirrorStyle,
      getDirectionalValue,
      createResponsiveStyles,
    };
  }, [contextIsRTL, globalConfig]);

  return helpers;
};
